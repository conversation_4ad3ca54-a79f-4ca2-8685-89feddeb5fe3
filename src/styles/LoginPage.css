/* LoginPage.css - 专业科技风格登录注册页面样式 */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Rajdhani:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* CSS变量定义 */
:root {
  --primary-color: #4dc8ff;
  --primary-dark: #2196f3;
  --secondary-color: #64b5f6;
  --accent-color: #00bcd4;
  --background-primary: #0a0f1c;
  --background-secondary: #1a1a2e;
  --background-tertiary: #16213e;
  --surface-color: rgba(15, 25, 40, 0.9);
  --surface-border: rgba(77, 200, 255, 0.25);
  --text-primary: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.8);
  --text-muted: rgba(255, 255, 255, 0.6);
  --error-color: #ff6b7a;
  --success-color: #4ecdc4;
  --warning-color: #ffd93d;
  --shadow-primary: 0 25px 80px rgba(0, 0, 0, 0.4);
  --shadow-secondary: 0 8px 32px rgba(77, 200, 255, 0.15);
  --border-radius: 16px;
  --border-radius-large: 24px;
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

/* 页面容器 - 优化性能和视觉效果 */
.login-page {
  min-height: 100vh;
  width: 100%;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, var(--background-primary) 0%, var(--background-secondary) 50%, var(--background-tertiary) 100%);
  display: flex;
  flex-direction: column;
  font-family: 'Inter', 'Rajdhani', sans-serif;
  color: var(--text-primary);
  /* GPU加速 */
  transform: translateZ(0);
  will-change: transform;
}

/* 3D背景 - 优化性能 */
.login-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  opacity: 0.85;
  /* GPU加速 */
  transform: translateZ(0);
  will-change: transform;
}

/* 顶部导航 - 改进设计和连接性 */
.login-header {
  position: relative;
  z-index: 10;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  background: rgba(10, 15, 28, 0.95);
  backdrop-filter: blur(25px);
  border-bottom: 1px solid var(--surface-border);
  /* 改进视觉连接 */
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.back-button,
.language-toggle {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 20px;
  background: var(--surface-color);
  border: 1px solid var(--surface-border);
  border-radius: var(--border-radius);
  color: var(--text-primary);
  text-decoration: none;
  font-family: 'Inter', sans-serif;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all var(--transition-normal);
  backdrop-filter: blur(15px);
  /* 玻璃态效果 */
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.back-button:hover,
.language-toggle:hover {
  background: rgba(77, 200, 255, 0.15);
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow:
    0 8px 25px rgba(77, 200, 255, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.back-icon,
.language-icon {
  font-size: 18px;
  opacity: 0.9;
}

/* 主要内容容器 */
.login-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
  z-index: 5;
  min-height: calc(100vh - 80px);
}

.login-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  max-width: 1400px;
  width: 100%;
  align-items: stretch;
  min-height: 600px;
}

/* 左侧品牌区域 */
.brand-section {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  padding: 40px;
}

.brand-content {
  max-width: 500px;
}

.brand-title {
  margin: 0 0 20px 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.welcome-text {
  font-family: 'Rajdhani', sans-serif;
  font-size: 24px;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.8);
  letter-spacing: 1px;
}

.app-name {
  font-family: 'Orbitron', monospace;
  font-size: 48px;
  font-weight: 700;
  background: linear-gradient(135deg, #4dc8ff, #00e5ff, #80d0ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: 2px;
  text-shadow: 0 0 30px rgba(77, 200, 255, 0.3);
}

.brand-subtitle {
  font-family: 'Rajdhani', sans-serif;
  font-size: 18px;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.6);
  margin: 0 0 40px 0;
  letter-spacing: 0.5px;
  line-height: 1.6;
}

/* 装饰性元素 */
.brand-decoration {
  margin-top: 40px;
}

.tech-lines {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tech-line {
  height: 2px;
  background: linear-gradient(90deg, #4dc8ff, transparent);
  border-radius: 1px;
  animation: techPulse 3s ease-in-out infinite;
}

.tech-line:nth-child(1) {
  width: 120px;
  animation-delay: 0s;
}

.tech-line:nth-child(2) {
  width: 80px;
  animation-delay: 0.5s;
}

.tech-line:nth-child(3) {
  width: 100px;
  animation-delay: 1s;
}

@keyframes techPulse {
  0%, 100% {
    opacity: 0.4;
    transform: scaleX(1);
  }
  50% {
    opacity: 1;
    transform: scaleX(1.1);
  }
}

/* 右侧表单区域 */
.form-section {
  display: flex;
  justify-content: center;
  align-items: center;
}

.form-container {
  background: rgba(15, 25, 40, 0.85);
  backdrop-filter: blur(25px);
  border: 1px solid rgba(77, 200, 255, 0.25);
  border-radius: 24px;
  padding: 32px;
  width: 100%;
  max-width: 520px;
  max-height: 85vh;
  overflow-y: auto;
  box-shadow:
    0 25px 80px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.12);
  position: relative;
  transition: all 0.3s ease;
}

.form-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(77, 200, 255, 0.05), transparent);
  animation: formShimmer 8s ease-in-out infinite;
  z-index: 0;
}

@keyframes formShimmer {
  0%, 90% {
    left: -100%;
  }
  10%, 80% {
    left: 100%;
  }
}

.form-header {
  text-align: center;
  margin-bottom: 24px;
  position: relative;
  z-index: 1;
}

.form-title {
  font-family: 'Orbitron', monospace;
  font-size: 26px;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 8px 0;
  letter-spacing: 1px;
  background: linear-gradient(135deg, #ffffff, #4dc8ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.form-subtitle {
  font-family: 'Rajdhani', sans-serif;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
  margin: 0;
  letter-spacing: 0.5px;
}

/* 表单样式 - 改进的输入字段设计 */
.login-form {
  position: relative;
  z-index: 1;
}

.form-group {
  margin-bottom: 24px;
  position: relative;
}

.form-group label {
  display: block;
  font-family: 'Inter', sans-serif;
  font-size: 13px;
  font-weight: 600;
  color: var(--text-secondary);
  margin-bottom: 8px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

/* 输入字段包装器 */
.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.form-group input {
  width: 100%;
  padding: 16px 20px;
  padding-right: 50px; /* 为图标留出空间 */
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid var(--surface-border);
  border-radius: var(--border-radius);
  color: var(--text-primary);
  font-family: 'Inter', sans-serif;
  font-size: 15px;
  font-weight: 400;
  transition: all var(--transition-normal);
  box-sizing: border-box;
  /* 玻璃态效果 */
  backdrop-filter: blur(10px);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.form-group input:focus {
  outline: none;
  border-color: var(--primary-color);
  background: rgba(255, 255, 255, 0.08);
  box-shadow:
    0 0 0 4px rgba(77, 200, 255, 0.15),
    0 8px 25px rgba(77, 200, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.form-group input:hover:not(:focus) {
  border-color: rgba(77, 200, 255, 0.4);
  background: rgba(255, 255, 255, 0.07);
  transform: translateY(-1px);
}

.form-group input::placeholder {
  color: var(--text-muted);
  transition: color var(--transition-normal);
}

.form-group input:focus::placeholder {
  color: var(--text-secondary);
}

/* 输入字段图标 */
.input-icon {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  pointer-events: none;
  transition: color var(--transition-normal);
}

.form-group input:focus + .input-icon {
  color: var(--primary-color);
}

/* 密码切换按钮 */
.password-toggle {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
}

.password-toggle:hover {
  color: var(--primary-color);
  background: rgba(77, 200, 255, 0.1);
}

/* 密码强度指示器 */
.password-strength {
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.password-strength-bar {
  flex: 1;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
  position: relative;
}

.password-strength-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  border-radius: 2px;
  transition: all var(--transition-normal);
}

.password-strength.weak .password-strength-bar::after {
  width: 33%;
  background: var(--error-color);
}

.password-strength.medium .password-strength-bar::after {
  width: 66%;
  background: var(--warning-color);
}

.password-strength.strong .password-strength-bar::after {
  width: 100%;
  background: var(--success-color);
}

.password-strength-text {
  font-family: 'Inter', sans-serif;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.password-strength.weak .password-strength-text {
  color: var(--error-color);
}

.password-strength.medium .password-strength-text {
  color: var(--warning-color);
}

.password-strength.strong .password-strength-text {
  color: var(--success-color);
}

/* 表单选项 */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 28px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 10px;
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  color: var(--text-secondary);
  cursor: pointer;
  transition: color var(--transition-normal);
}

.checkbox-label:hover {
  color: var(--text-primary);
}

.checkbox-label input[type="checkbox"] {
  width: 18px;
  height: 18px;
  margin: 0;
  accent-color: var(--primary-color);
}

.forgot-password {
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  color: var(--primary-color);
  background: none;
  border: none;
  cursor: pointer;
  transition: color var(--transition-normal);
  text-decoration: none;
  padding: 4px 8px;
  border-radius: 4px;
}

.forgot-password:hover {
  color: var(--secondary-color);
  background: rgba(77, 200, 255, 0.1);
}

/* 提交按钮 - 改进设计 */
.submit-button {
  width: 100%;
  padding: 18px 24px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  border: none;
  border-radius: var(--border-radius);
  color: var(--text-primary);
  font-family: 'Inter', sans-serif;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 0.5px;
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  /* 玻璃态效果 */
  box-shadow:
    var(--shadow-secondary),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.submit-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition-slow);
}

.submit-button:hover:not(:disabled)::before {
  left: 100%;
}

.submit-button:hover:not(:disabled) {
  transform: translateY(-3px);
  box-shadow:
    0 12px 35px rgba(77, 200, 255, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.submit-button:active:not(:disabled) {
  transform: translateY(-1px);
}

.submit-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 加载状态 */
.loading-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid var(--text-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 分割线 - 改进设计 */
.form-divider {
  display: flex;
  align-items: center;
  margin: 28px 0;
  gap: 16px;
}

.divider-line {
  flex: 1;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--surface-border), transparent);
}

.divider-text {
  font-family: 'Inter', sans-serif;
  font-size: 13px;
  font-weight: 500;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 1px;
  padding: 0 8px;
}

/* 社交登录按钮 - 改进设计 */
.social-login {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 28px;
}

.social-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  width: 100%;
  padding: 14px 20px;
  background: var(--surface-color);
  border: 1px solid var(--surface-border);
  border-radius: var(--border-radius);
  color: var(--text-primary);
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-normal);
  backdrop-filter: blur(10px);
  /* 玻璃态效果 */
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.social-button:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.social-button.google:hover {
  border-color: #4285f4;
  box-shadow:
    0 8px 25px rgba(66, 133, 244, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.social-button.github:hover {
  border-color: #333;
  box-shadow:
    0 8px 25px rgba(51, 51, 51, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* 表单切换 */
.form-switch {
  text-align: center;
}

.switch-button {
  background: none;
  border: none;
  color: #4dc8ff;
  font-family: 'Rajdhani', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: color 0.3s ease;
  text-decoration: underline;
}

.switch-button:hover {
  color: #80d0ff;
}

/* 中文字体优化 */
.login-page[data-lang="zh"] .form-group label,
.login-page[data-lang="zh"] .form-group input,
.login-page[data-lang="zh"] .social-button,
.login-page[data-lang="zh"] .switch-button,
.login-page[data-lang="zh"] .back-button,
.login-page[data-lang="zh"] .language-toggle {
  font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
}

.login-page[data-lang="zh"] .welcome-text,
.login-page[data-lang="zh"] .brand-subtitle {
  font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  letter-spacing: 0.5px;
}

/* 表单进度指示器 */
.form-progress {
  width: 100%;
  height: 3px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  margin-bottom: 24px;
  overflow: hidden;
}

.form-progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  border-radius: 2px;
  transition: width var(--transition-normal);
  box-shadow: 0 0 10px rgba(77, 200, 255, 0.5);
}

/* 表单验证状态 */
.form-group input.error {
  border-color: var(--error-color);
  background: rgba(255, 107, 122, 0.08);
  box-shadow: 0 0 0 4px rgba(255, 107, 122, 0.15);
  animation: shakeError 0.5s ease-in-out;
}

.form-group input.success {
  border-color: var(--success-color);
  background: rgba(78, 205, 196, 0.08);
  box-shadow: 0 0 0 4px rgba(78, 205, 196, 0.15);
}

/* 错误消息样式 */
.error-message {
  color: var(--error-color);
  font-family: 'Inter', sans-serif;
  font-size: 12px;
  font-weight: 500;
  margin-top: 6px;
  display: flex;
  align-items: center;
  gap: 6px;
  animation: fadeInError 0.3s ease-out;
}

.error-message svg {
  flex-shrink: 0;
}

.submit-error {
  background: rgba(255, 107, 122, 0.1);
  border: 1px solid rgba(255, 107, 122, 0.3);
  border-radius: var(--border-radius);
  padding: 12px 16px;
  margin-bottom: 16px;
  backdrop-filter: blur(10px);
}

/* 动画定义 */
@keyframes shakeError {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-4px); }
  75% { transform: translateX(4px); }
}

@keyframes fadeInError {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 增强的动画效果 */
.login-container {
  animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 自定义滚动条 */
.form-container::-webkit-scrollbar {
  width: 6px;
}

.form-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

.form-container::-webkit-scrollbar-thumb {
  background: rgba(77, 200, 255, 0.3);
  border-radius: 3px;
  transition: background 0.3s ease;
}

.form-container::-webkit-scrollbar-thumb:hover {
  background: rgba(77, 200, 255, 0.5);
}

/* 表单分组样式 */
.form-section-divider {
  margin: 24px 0;
  text-align: center;
  position: relative;
}

.form-section-divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(77, 200, 255, 0.3), transparent);
}

.form-section-title {
  background: rgba(15, 25, 40, 0.9);
  padding: 0 12px;
  font-family: 'Rajdhani', sans-serif;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
  z-index: 1;
}



/* 响应式设计 */
@media screen and (max-width: 1024px) {
  .login-content {
    grid-template-columns: 1fr;
    gap: 40px;
    max-width: 600px;
    min-height: auto;
  }

  .brand-section {
    order: 2;
    padding: 20px;
    text-align: center;
  }

  .form-section {
    order: 1;
  }

  .form-container {
    max-width: 100%;
    margin: 0 auto;
  }

  .app-name {
    font-size: 36px;
  }

  .welcome-text {
    font-size: 20px;
  }
}

@media screen and (max-width: 768px) {
  .login-container {
    padding: 15px;
    min-height: calc(100vh - 70px);
  }

  .login-header {
    padding: 15px 20px;
  }

  .form-container {
    padding: 24px;
    border-radius: 20px;
    max-height: 90vh;
  }

  .form-title {
    font-size: 22px;
  }

  .app-name {
    font-size: 28px;
  }

  .welcome-text {
    font-size: 18px;
  }

  .brand-subtitle {
    font-size: 16px;
  }

  .form-group {
    margin-bottom: 16px;
  }

  .form-group input {
    padding: 11px 14px;
    font-size: 14px;
  }

  .submit-button {
    padding: 12px;
    font-size: 14px;
  }
}

@media screen and (max-width: 480px) {
  .login-container {
    padding: 10px;
  }

  .form-container {
    padding: 20px;
    border-radius: 16px;
  }

  .form-title {
    font-size: 20px;
  }

  .app-name {
    font-size: 24px;
  }

  .welcome-text {
    font-size: 16px;
  }

  .brand-subtitle {
    font-size: 14px;
  }

  .form-group input {
    padding: 10px 12px;
  }

  .social-login {
    flex-direction: column;
    gap: 10px;
  }

  .social-button {
    padding: 10px;
    font-size: 13px;
  }
}

/* 动画增强 */
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.form-container {
  animation: slideInRight 0.6s ease-out 0.2s both;
}

.brand-section {
  animation: slideInLeft 0.6s ease-out both;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}


